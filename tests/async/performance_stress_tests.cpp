#include <gmock/gmock.h>
#include <gtest/gtest.h>

#include <algorithm>
#include <atomic>
#include <chrono>
#include <future>
#include <memory>
#include <numeric>
#include <random>
#include <thread>
#include <vector>

#include "atom/async/async.hpp"
#include "atom/async/message_bus.hpp"
#include "atom/async/promise.hpp"

using namespace atom::async;
using namespace std::chrono_literals;

// Test fixture for performance and stress tests
class AsyncPerformanceTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Setup performance test data
        large_dataset.resize(100000);
        std::iota(large_dataset.begin(), large_dataset.end(), 1);
        
        // Random data for stress testing
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(1, 1000);
        
        random_dataset.resize(50000);
        for (auto& val : random_dataset) {
            val = dis(gen);
        }
    }

    void TearDown() override {
        // Cleanup
    }

    std::vector<int> large_dataset;
    std::vector<int> random_dataset;
};

// ============================================================================
// AsyncWorker Performance Tests
// ============================================================================

TEST_F(AsyncPerformanceTest, AsyncWorkerThroughput) {
    const int num_workers = 1000;
    AsyncWorkerManager<int> manager;
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    std::vector<std::shared_ptr<AsyncWorker<int>>> workers;
    workers.reserve(num_workers);
    
    // Create many workers rapidly
    for (int i = 0; i < num_workers; ++i) {
        auto worker = manager.createWorker([i]() {
            // Minimal work to test throughput
            return i * 2;
        });
        workers.push_back(worker);
    }
    
    // Wait for all to complete
    manager.waitForAll(10s);
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    // Verify all completed correctly
    EXPECT_TRUE(manager.allDone());
    for (int i = 0; i < num_workers; ++i) {
        EXPECT_EQ(workers[i]->getResult(), i * 2);
    }
    
    // Performance metrics
    double workers_per_second = static_cast<double>(num_workers) / (duration.count() / 1000.0);
    EXPECT_GT(workers_per_second, 100);  // At least 100 workers per second
    EXPECT_LT(duration.count(), 10000);  // Should complete within 10 seconds
    
    std::cout << "AsyncWorker Throughput: " << workers_per_second 
              << " workers/second (" << duration.count() << "ms total)" << std::endl;
}

TEST_F(AsyncPerformanceTest, AsyncWorkerLatency) {
    const int num_measurements = 100;
    std::vector<std::chrono::microseconds> latencies;
    latencies.reserve(num_measurements);
    
    for (int i = 0; i < num_measurements; ++i) {
        AsyncWorker<int> worker;
        
        auto start_time = std::chrono::high_resolution_clock::now();
        worker.startAsync([]() { return 42; });
        auto result = worker.getResult();
        auto end_time = std::chrono::high_resolution_clock::now();
        
        auto latency = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
        latencies.push_back(latency);
        
        EXPECT_EQ(result, 42);
    }
    
    // Calculate statistics
    auto total_latency = std::accumulate(latencies.begin(), latencies.end(), 
                                       std::chrono::microseconds(0));
    auto avg_latency = total_latency / num_measurements;
    
    std::sort(latencies.begin(), latencies.end());
    auto median_latency = latencies[num_measurements / 2];
    auto p95_latency = latencies[static_cast<size_t>(num_measurements * 0.95)];
    
    EXPECT_LT(avg_latency.count(), 10000);  // Average < 10ms
    EXPECT_LT(p95_latency.count(), 50000);  // 95th percentile < 50ms
    
    std::cout << "AsyncWorker Latency - Avg: " << avg_latency.count() 
              << "μs, Median: " << median_latency.count() 
              << "μs, P95: " << p95_latency.count() << "μs" << std::endl;
}

// ============================================================================
// Promise Performance Tests
// ============================================================================

TEST_F(AsyncPerformanceTest, PromiseCreationPerformance) {
    const int num_promises = 10000;
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    std::vector<std::unique_ptr<Promise<int>>> promises;
    promises.reserve(num_promises);
    
    for (int i = 0; i < num_promises; ++i) {
        promises.push_back(std::make_unique<Promise<int>>());
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
    
    double promises_per_second = static_cast<double>(num_promises) / (duration.count() / 1000000.0);
    EXPECT_GT(promises_per_second, 100000);  // At least 100k promises per second
    
    std::cout << "Promise Creation: " << promises_per_second 
              << " promises/second" << std::endl;
}

TEST_F(AsyncPerformanceTest, PromiseSetValuePerformance) {
    const int num_promises = 5000;
    std::vector<std::unique_ptr<Promise<int>>> promises;
    std::vector<std::future<int>> futures;
    
    // Create promises
    for (int i = 0; i < num_promises; ++i) {
        auto promise = std::make_unique<Promise<int>>();
        futures.push_back(promise->getFuture());
        promises.push_back(std::move(promise));
    }
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // Set values rapidly
    for (int i = 0; i < num_promises; ++i) {
        promises[i]->setValue(i);
    }
    
    // Wait for all to complete
    for (int i = 0; i < num_promises; ++i) {
        EXPECT_EQ(futures[i].get(), i);
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
    
    double operations_per_second = static_cast<double>(num_promises) / (duration.count() / 1000000.0);
    EXPECT_GT(operations_per_second, 50000);  // At least 50k operations per second
    
    std::cout << "Promise SetValue: " << operations_per_second 
              << " operations/second" << std::endl;
}

// ============================================================================
// MessageBus Performance Tests
// ============================================================================

TEST_F(AsyncPerformanceTest, MessageBusPublishPerformance) {
    auto message_bus = std::make_shared<MessageBus>();
    const int num_messages = 10000;
    
    std::atomic<int> received_count{0};
    
    // Subscribe to messages
    auto token = message_bus->subscribe<int>("perf_topic", [&](const int&) {
        received_count++;
    });
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // Publish messages rapidly
    for (int i = 0; i < num_messages; ++i) {
        message_bus->publish("perf_topic", i);
    }
    
    auto publish_end_time = std::chrono::high_resolution_clock::now();
    
    // Wait for all messages to be processed
    while (received_count.load() < num_messages) {
        std::this_thread::sleep_for(1ms);
    }
    
    auto process_end_time = std::chrono::high_resolution_clock::now();
    
    auto publish_duration = std::chrono::duration_cast<std::chrono::microseconds>(
        publish_end_time - start_time);
    auto total_duration = std::chrono::duration_cast<std::chrono::microseconds>(
        process_end_time - start_time);
    
    double publish_rate = static_cast<double>(num_messages) / (publish_duration.count() / 1000000.0);
    double process_rate = static_cast<double>(num_messages) / (total_duration.count() / 1000000.0);
    
    EXPECT_EQ(received_count.load(), num_messages);
    EXPECT_GT(publish_rate, 10000);  // At least 10k publishes per second
    EXPECT_GT(process_rate, 5000);   // At least 5k processed per second
    
    std::cout << "MessageBus Publish Rate: " << publish_rate 
              << " msg/sec, Process Rate: " << process_rate << " msg/sec" << std::endl;
}

// ============================================================================
// Memory and Resource Stress Tests
// ============================================================================

TEST_F(AsyncPerformanceTest, MemoryStressTest) {
    const int num_iterations = 100;
    const int workers_per_iteration = 100;
    
    for (int iter = 0; iter < num_iterations; ++iter) {
        AsyncWorkerManager<std::vector<int>> manager;
        std::vector<std::shared_ptr<AsyncWorker<std::vector<int>>>> workers;
        
        // Create workers that allocate memory
        for (int i = 0; i < workers_per_iteration; ++i) {
            auto worker = manager.createWorker([i]() {
                std::vector<int> data(1000, i);
                return data;
            });
            workers.push_back(worker);
        }
        
        // Wait for completion
        manager.waitForAll(5s);
        
        // Verify results
        for (int i = 0; i < workers_per_iteration; ++i) {
            auto result = workers[i]->getResult();
            EXPECT_EQ(result.size(), 1000);
            EXPECT_EQ(result[0], i);
        }
        
        // Cleanup happens automatically when manager goes out of scope
    }
    
    // If we reach here without crashes, memory management is working
    SUCCEED();
}

TEST_F(AsyncPerformanceTest, ConcurrentStressTest) {
    const int num_threads = std::thread::hardware_concurrency();
    const int operations_per_thread = 1000;
    
    std::atomic<int> total_operations{0};
    std::atomic<int> successful_operations{0};
    std::vector<std::thread> threads;
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    for (int t = 0; t < num_threads; ++t) {
        threads.emplace_back([&, t]() {
            AsyncWorkerManager<int> manager;
            
            for (int i = 0; i < operations_per_thread; ++i) {
                try {
                    auto worker = manager.createWorker([t, i]() {
                        // Simulate varying workload
                        if ((t + i) % 10 == 0) {
                            std::this_thread::sleep_for(1ms);
                        }
                        return t * 1000 + i;
                    });
                    
                    int result = worker->getResult();
                    if (result == t * 1000 + i) {
                        successful_operations++;
                    }
                    total_operations++;
                } catch (...) {
                    total_operations++;
                }
            }
        });
    }
    
    for (auto& thread : threads) {
        thread.join();
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    int expected_operations = num_threads * operations_per_thread;
    EXPECT_EQ(total_operations.load(), expected_operations);
    EXPECT_EQ(successful_operations.load(), expected_operations);
    
    double operations_per_second = static_cast<double>(expected_operations) / (duration.count() / 1000.0);
    EXPECT_GT(operations_per_second, 1000);  // At least 1000 operations per second
    
    std::cout << "Concurrent Stress Test: " << operations_per_second 
              << " operations/second with " << num_threads << " threads" << std::endl;
}

// ============================================================================
// Scalability Tests
// ============================================================================

TEST_F(AsyncPerformanceTest, ScalabilityTest) {
    std::vector<int> worker_counts = {10, 50, 100, 500, 1000};
    
    for (int count : worker_counts) {
        AsyncWorkerManager<int> manager;
        
        auto start_time = std::chrono::high_resolution_clock::now();
        
        std::vector<std::shared_ptr<AsyncWorker<int>>> workers;
        for (int i = 0; i < count; ++i) {
            auto worker = manager.createWorker([i]() { return i; });
            workers.push_back(worker);
        }
        
        manager.waitForAll(30s);
        
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        
        // Verify all completed
        EXPECT_TRUE(manager.allDone());
        
        double throughput = static_cast<double>(count) / (duration.count() / 1000.0);
        
        std::cout << "Workers: " << count << ", Time: " << duration.count() 
                  << "ms, Throughput: " << throughput << " workers/sec" << std::endl;
        
        // Performance should not degrade significantly with scale
        EXPECT_LT(duration.count(), count * 10);  // Should not take more than 10ms per worker
    }
}
